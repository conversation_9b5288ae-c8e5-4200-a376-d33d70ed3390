2025-06-14 13:38:17 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:17 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:17 | INFO | Data collector initialized
2025-06-14 13:38:17 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:17 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:17 | INFO | Data collector initialized
2025-06-14 13:38:17 | INFO | Feature engineer initialized
2025-06-14 13:38:17 | INFO | Using device: cpu
2025-06-14 13:38:17 | INFO | Model trainer initialized
2025-06-14 13:38:17 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:17 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:17 | INFO | Data collector initialized
2025-06-14 13:38:17 | INFO | Feature engineer initialized
2025-06-14 13:38:17 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:17 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:17 | INFO | Data collector initialized
2025-06-14 13:38:17 | INFO | Feature engineer initialized
2025-06-14 13:38:17 | INFO | Using device: cpu
2025-06-14 13:38:17 | INFO | Model trainer initialized
2025-06-14 13:38:17 | INFO | Multi-timeframe validator initialized
2025-06-14 13:38:17 | INFO | Signal generator initialized
2025-06-14 13:38:17 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:17 | INFO | Telegram bot initialized
2025-06-14 13:38:17 | INFO | Forex AI System initialized successfully
2025-06-14 13:38:17 | INFO | Starting historical data collection...
2025-06-14 13:38:17 | INFO | Database tables created successfully
2025-06-14 13:38:17 | INFO | Collecting 15m data for XAUUSD
2025-06-14 13:38:17 | INFO | Collecting historical data for XAUUSD 15m (5 years)
2025-06-14 13:38:17 | INFO | Fetching GC=F data from yfinance (15m, 5y)
2025-06-14 13:38:18 | WARNING | No data received from yfinance for GC=F
2025-06-14 13:38:18 | WARNING | Primary source (yfinance) failed, trying secondary source
2025-06-14 13:38:18 | WARNING | Alpha Vantage doesn't support XAUUSD directly
2025-06-14 13:38:18 | WARNING | Secondary source failed, trying backup source
2025-06-14 13:38:18 | INFO | Fetching XAU/USD data from TwelveData (15min)
2025-06-14 13:38:19 | INFO | Successfully fetched 5000 records from TwelveData
2025-06-14 13:38:20 | INFO | Inserted 5000 records for XAUUSD 15m
2025-06-14 13:38:20 | INFO | Historical data stored in database for XAUUSD 15m
2025-06-14 13:38:20 | INFO | Collecting 1h data for XAUUSD
2025-06-14 13:38:20 | INFO | Collecting historical data for XAUUSD 1h (5 years)
2025-06-14 13:38:20 | INFO | Fetching GC=F data from yfinance (1h, 5y)
2025-06-14 13:38:20 | WARNING | No data received from yfinance for GC=F
2025-06-14 13:38:20 | WARNING | Primary source (yfinance) failed, trying secondary source
2025-06-14 13:38:20 | INFO | Rate limiting alpha_vantage: sleeping for 10.01 seconds
2025-06-14 13:38:30 | WARNING | Alpha Vantage doesn't support XAUUSD directly
2025-06-14 13:38:30 | WARNING | Secondary source failed, trying backup source
2025-06-14 13:38:30 | INFO | Fetching XAU/USD data from TwelveData (1h)
2025-06-14 13:38:31 | INFO | Successfully fetched 5000 records from TwelveData
2025-06-14 13:38:32 | INFO | Inserted 5000 records for XAUUSD 1h
2025-06-14 13:38:32 | INFO | Historical data stored in database for XAUUSD 1h
2025-06-14 13:38:32 | INFO | Collecting 4h data for XAUUSD
2025-06-14 13:38:32 | INFO | Collecting historical data for XAUUSD 4h (5 years)
2025-06-14 13:38:32 | INFO | Fetching GC=F data from yfinance (4h, 5y)
2025-06-14 13:38:32 | WARNING | No data received from yfinance for GC=F
2025-06-14 13:38:32 | WARNING | Primary source (yfinance) failed, trying secondary source
2025-06-14 13:38:32 | INFO | Rate limiting alpha_vantage: sleeping for 10.05 seconds
2025-06-14 13:38:42 | WARNING | Alpha Vantage doesn't support XAUUSD directly
2025-06-14 13:38:42 | WARNING | Secondary source failed, trying backup source
2025-06-14 13:38:42 | INFO | Fetching XAU/USD data from TwelveData (4h)
2025-06-14 13:38:43 | INFO | Successfully fetched 5000 records from TwelveData
2025-06-14 13:38:44 | INFO | Inserted 5000 records for XAUUSD 4h
2025-06-14 13:38:44 | INFO | Historical data stored in database for XAUUSD 4h
2025-06-14 13:38:44 | INFO | Historical data collection completed
2025-06-14 13:38:58 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:58 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:58 | INFO | Data collector initialized
2025-06-14 13:38:58 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:58 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:58 | INFO | Data collector initialized
2025-06-14 13:38:58 | INFO | Feature engineer initialized
2025-06-14 13:38:58 | INFO | Using device: cpu
2025-06-14 13:38:58 | INFO | Model trainer initialized
2025-06-14 13:38:58 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:58 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:58 | INFO | Data collector initialized
2025-06-14 13:38:58 | INFO | Feature engineer initialized
2025-06-14 13:38:58 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:58 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:58 | INFO | Data collector initialized
2025-06-14 13:38:58 | INFO | Feature engineer initialized
2025-06-14 13:38:58 | INFO | Using device: cpu
2025-06-14 13:38:58 | INFO | Model trainer initialized
2025-06-14 13:38:58 | INFO | Multi-timeframe validator initialized
2025-06-14 13:38:58 | INFO | Signal generator initialized
2025-06-14 13:38:58 | INFO | Database manager initialized with sqlite
2025-06-14 13:38:58 | INFO | Telegram bot initialized
2025-06-14 13:38:58 | INFO | Forex AI System initialized successfully
2025-06-14 13:38:58 | INFO | Starting model training...
2025-06-14 13:38:58 | INFO | Training transformer model...
2025-06-14 13:38:58 | INFO | Starting training for transformer model
2025-06-14 13:38:58 | INFO | Preparing training data...
2025-06-14 13:38:59 | INFO | Loaded data - 15m: 5000, 1h: 5000, 4h: 5000
2025-06-14 13:38:59 | INFO | Starting feature engineering pipeline
2025-06-14 13:38:59 | INFO | Technical indicators calculated successfully
2025-06-14 13:38:59 | INFO | Price action features calculated successfully
2025-06-14 13:38:59 | INFO | Candlestick patterns detected successfully
2025-06-14 13:38:59 | INFO | Market regime features calculated successfully
2025-06-14 13:38:59 | INFO | Technical indicators calculated successfully
2025-06-14 13:38:59 | INFO | Market regime features calculated successfully
2025-06-14 13:38:59 | INFO | Technical indicators calculated successfully
2025-06-14 13:38:59 | INFO | Market regime features calculated successfully
2025-06-14 13:38:59 | INFO | Multi-timeframe features created successfully
2025-06-14 13:38:59 | INFO | Removed 300 rows with NaN values
2025-06-14 13:38:59 | INFO | Feature engineering completed. Final dataset shape: (4700, 94)
2025-06-14 13:38:59 | INFO | Feature engineering completed. Shape: (4700, 94)
2025-06-14 13:38:59 | INFO | Prepared 4600 sequences with shape (4600, 100, 92)
2025-06-14 13:38:59 | INFO | Training data prepared: X shape torch.Size([4600, 100, 92]), y shape torch.Size([4600])
2025-06-14 13:38:59 | INFO | Starting hyperparameter optimization for transformer
2025-06-14 13:38:59 | INFO | Transformer model initialized with 4813444 parameters
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Data collector initialized
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Data collector initialized
2025-06-14 13:45:15 | INFO | Feature engineer initialized
2025-06-14 13:45:15 | INFO | Using device: cpu
2025-06-14 13:45:15 | INFO | Model trainer initialized
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Data collector initialized
2025-06-14 13:45:15 | INFO | Feature engineer initialized
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Data collector initialized
2025-06-14 13:45:15 | INFO | Feature engineer initialized
2025-06-14 13:45:15 | INFO | Using device: cpu
2025-06-14 13:45:15 | INFO | Model trainer initialized
2025-06-14 13:45:15 | INFO | Multi-timeframe validator initialized
2025-06-14 13:45:15 | INFO | Signal generator initialized
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Telegram bot initialized
2025-06-14 13:45:15 | INFO | Forex AI System initialized successfully
2025-06-14 13:45:15 | INFO | Starting Forex AI System in dashboard mode
2025-06-14 13:45:15 | INFO | Starting web dashboard...
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Data collector initialized
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Data collector initialized
2025-06-14 13:45:15 | INFO | Feature engineer initialized
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Database manager initialized with sqlite
2025-06-14 13:45:15 | INFO | Data collector initialized
2025-06-14 13:45:15 | INFO | Feature engineer initialized
2025-06-14 13:45:15 | INFO | Using device: cpu
2025-06-14 13:45:15 | INFO | Model trainer initialized
2025-06-14 13:45:15 | INFO | Multi-timeframe validator initialized
2025-06-14 13:45:15 | INFO | Signal generator initialized
