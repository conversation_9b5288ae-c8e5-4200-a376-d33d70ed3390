"""
Database management for Forex AI System
"""

import asyncio
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
import pandas as pd
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, Float, String, DateTime, Boolean, Text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from loguru import logger

from .config import Config

Base = declarative_base()


class HistoricalData(Base):
    """Historical price data table"""
    __tablename__ = 'historical_data'
    
    id = Column(Integer, primary_key=True)
    symbol = Column(String(10), nullable=False)
    timeframe = Column(String(5), nullable=False)
    timestamp = Column(DateTime, nullable=False)
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(Float, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)


class Signals(Base):
    """Trading signals table"""
    __tablename__ = 'signals'
    
    id = Column(Integer, primary_key=True)
    symbol = Column(String(10), nullable=False)
    direction = Column(String(4), nullable=False)  # BUY/SELL
    confidence = Column(Float, nullable=False)
    entry_price = Column(Float, nullable=False)
    stop_loss = Column(Float, nullable=False)
    take_profit = Column(Float, nullable=False)
    h4_trend = Column(String(20))
    h1_momentum = Column(String(20))
    m15_pattern = Column(String(50))
    model_used = Column(String(50))
    features = Column(Text)  # JSON string of features
    timestamp = Column(DateTime, default=datetime.utcnow)
    status = Column(String(20), default='ACTIVE')  # ACTIVE, CLOSED, CANCELLED


class Trades(Base):
    """Executed trades table"""
    __tablename__ = 'trades'
    
    id = Column(Integer, primary_key=True)
    signal_id = Column(Integer, nullable=False)
    symbol = Column(String(10), nullable=False)
    direction = Column(String(4), nullable=False)
    entry_price = Column(Float, nullable=False)
    exit_price = Column(Float)
    stop_loss = Column(Float, nullable=False)
    take_profit = Column(Float, nullable=False)
    quantity = Column(Float, nullable=False)
    pnl = Column(Float)
    pnl_pips = Column(Float)
    entry_time = Column(DateTime, nullable=False)
    exit_time = Column(DateTime)
    status = Column(String(20), default='OPEN')  # OPEN, CLOSED, CANCELLED
    exit_reason = Column(String(20))  # TP, SL, MANUAL, TIMEOUT


class ModelPerformance(Base):
    """Model performance tracking table"""
    __tablename__ = 'model_performance'
    
    id = Column(Integer, primary_key=True)
    model_name = Column(String(50), nullable=False)
    version = Column(String(20), nullable=False)
    accuracy = Column(Float)
    precision = Column(Float)
    recall = Column(Float)
    f1_score = Column(Float)
    sharpe_ratio = Column(Float)
    max_drawdown = Column(Float)
    win_rate = Column(Float)
    profit_factor = Column(Float)
    total_trades = Column(Integer)
    training_date = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=False)


class FeatureImportance(Base):
    """Feature importance tracking table"""
    __tablename__ = 'feature_importance'
    
    id = Column(Integer, primary_key=True)
    model_name = Column(String(50), nullable=False)
    feature_name = Column(String(100), nullable=False)
    importance_score = Column(Float, nullable=False)
    timeframe = Column(String(5))
    created_at = Column(DateTime, default=datetime.utcnow)


class DatabaseManager:
    """Database management class"""
    
    def __init__(self, config: Config):
        """Initialize database manager"""
        self.config = config
        self.db_path = config.database.path
        
        # Create data directory if it doesn't exist
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Create database engine
        if config.database.type == "sqlite":
            self.engine = create_engine(f"sqlite:///{self.db_path}")
        else:
            self.engine = create_engine(self.db_path)
        
        logger.info(f"Database manager initialized with {config.database.type}")
    
    async def create_tables(self):
        """Create all database tables"""
        try:
            Base.metadata.create_all(self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Error creating database tables: {e}")
            raise
    
    def get_session(self):
        """Get database session"""
        Session = sessionmaker(bind=self.engine)
        return Session()
    
    async def insert_historical_data(self, data: pd.DataFrame, symbol: str, timeframe: str):
        """Insert historical data into database"""
        try:
            session = self.get_session()
            
            for _, row in data.iterrows():
                historical_data = HistoricalData(
                    symbol=symbol,
                    timeframe=timeframe,
                    timestamp=row['timestamp'],
                    open=row['open'],
                    high=row['high'],
                    low=row['low'],
                    close=row['close'],
                    volume=row.get('volume', 0)
                )
                session.add(historical_data)
            
            session.commit()
            session.close()
            
            logger.info(f"Inserted {len(data)} records for {symbol} {timeframe}")
            
        except Exception as e:
            logger.error(f"Error inserting historical data: {e}")
            if session:
                session.rollback()
                session.close()
            raise
    
    async def get_historical_data(self, symbol: str, timeframe: str, limit: Optional[int] = None) -> pd.DataFrame:
        """Get historical data from database"""
        try:
            session = self.get_session()
            
            query = session.query(HistoricalData).filter(
                HistoricalData.symbol == symbol,
                HistoricalData.timeframe == timeframe
            ).order_by(HistoricalData.timestamp.desc())
            
            if limit:
                query = query.limit(limit)
            
            results = query.all()
            session.close()
            
            # Convert to DataFrame
            data = []
            for row in results:
                data.append({
                    'timestamp': row.timestamp,
                    'open': row.open,
                    'high': row.high,
                    'low': row.low,
                    'close': row.close,
                    'volume': row.volume
                })
            
            df = pd.DataFrame(data)
            if not df.empty:
                df = df.sort_values('timestamp').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            logger.error(f"Error getting historical data: {e}")
            raise
    
    async def insert_signal(self, signal_data: Dict[str, Any]) -> int:
        """Insert trading signal into database"""
        try:
            session = self.get_session()
            
            signal = Signals(
                symbol=signal_data['symbol'],
                direction=signal_data['direction'],
                confidence=signal_data['confidence'],
                entry_price=signal_data['entry_price'],
                stop_loss=signal_data['stop_loss'],
                take_profit=signal_data['take_profit'],
                h4_trend=signal_data.get('h4_trend'),
                h1_momentum=signal_data.get('h1_momentum'),
                m15_pattern=signal_data.get('m15_pattern'),
                model_used=signal_data.get('model_used'),
                features=signal_data.get('features')
            )
            
            session.add(signal)
            session.commit()
            
            signal_id = signal.id
            session.close()
            
            logger.info(f"Signal inserted with ID: {signal_id}")
            return signal_id
            
        except Exception as e:
            logger.error(f"Error inserting signal: {e}")
            if session:
                session.rollback()
                session.close()
            raise
    
    async def get_active_signals(self) -> List[Dict[str, Any]]:
        """Get active trading signals"""
        try:
            session = self.get_session()
            
            signals = session.query(Signals).filter(
                Signals.status == 'ACTIVE'
            ).all()
            
            session.close()
            
            # Convert to list of dictionaries
            result = []
            for signal in signals:
                result.append({
                    'id': signal.id,
                    'symbol': signal.symbol,
                    'direction': signal.direction,
                    'confidence': signal.confidence,
                    'entry_price': signal.entry_price,
                    'stop_loss': signal.stop_loss,
                    'take_profit': signal.take_profit,
                    'timestamp': signal.timestamp,
                    'model_used': signal.model_used
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting active signals: {e}")
            raise
    
    async def insert_trade(self, trade_data: Dict[str, Any]) -> int:
        """Insert trade record into database"""
        try:
            session = self.get_session()
            
            trade = Trades(
                signal_id=trade_data['signal_id'],
                symbol=trade_data['symbol'],
                direction=trade_data['direction'],
                entry_price=trade_data['entry_price'],
                exit_price=trade_data.get('exit_price'),
                stop_loss=trade_data['stop_loss'],
                take_profit=trade_data['take_profit'],
                quantity=trade_data['quantity'],
                pnl=trade_data.get('pnl'),
                pnl_pips=trade_data.get('pnl_pips'),
                entry_time=trade_data['entry_time'],
                exit_time=trade_data.get('exit_time'),
                status=trade_data.get('status', 'OPEN'),
                exit_reason=trade_data.get('exit_reason')
            )
            
            session.add(trade)
            session.commit()
            
            trade_id = trade.id
            session.close()
            
            logger.info(f"Trade inserted with ID: {trade_id}")
            return trade_id
            
        except Exception as e:
            logger.error(f"Error inserting trade: {e}")
            if session:
                session.rollback()
                session.close()
            raise
    
    async def update_model_performance(self, performance_data: Dict[str, Any]):
        """Update model performance metrics"""
        try:
            session = self.get_session()
            
            # Deactivate previous versions
            session.query(ModelPerformance).filter(
                ModelPerformance.model_name == performance_data['model_name']
            ).update({'is_active': False})
            
            # Insert new performance record
            performance = ModelPerformance(
                model_name=performance_data['model_name'],
                version=performance_data['version'],
                accuracy=performance_data.get('accuracy'),
                precision=performance_data.get('precision'),
                recall=performance_data.get('recall'),
                f1_score=performance_data.get('f1_score'),
                sharpe_ratio=performance_data.get('sharpe_ratio'),
                max_drawdown=performance_data.get('max_drawdown'),
                win_rate=performance_data.get('win_rate'),
                profit_factor=performance_data.get('profit_factor'),
                total_trades=performance_data.get('total_trades'),
                is_active=True
            )
            
            session.add(performance)
            session.commit()
            session.close()
            
            logger.info(f"Model performance updated for {performance_data['model_name']}")
            
        except Exception as e:
            logger.error(f"Error updating model performance: {e}")
            if session:
                session.rollback()
                session.close()
            raise
    
    async def get_trade_statistics(self, days: int = 30) -> Dict[str, Any]:
        """Get trading statistics for the last N days"""
        try:
            session = self.get_session()
            
            # Calculate date threshold
            from datetime import datetime, timedelta
            date_threshold = datetime.utcnow() - timedelta(days=days)
            
            # Get trades from the last N days
            trades = session.query(Trades).filter(
                Trades.entry_time >= date_threshold
            ).all()
            
            session.close()
            
            if not trades:
                return {
                    'total_trades': 0,
                    'win_rate': 0,
                    'total_pnl': 0,
                    'avg_pnl_per_trade': 0,
                    'max_drawdown': 0,
                    'profit_factor': 0
                }
            
            # Calculate statistics
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t.pnl and t.pnl > 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            total_pnl = sum([t.pnl for t in trades if t.pnl])
            avg_pnl_per_trade = total_pnl / total_trades if total_trades > 0 else 0
            
            # Calculate profit factor
            gross_profit = sum([t.pnl for t in trades if t.pnl and t.pnl > 0])
            gross_loss = abs(sum([t.pnl for t in trades if t.pnl and t.pnl < 0]))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
            
            return {
                'total_trades': total_trades,
                'win_rate': win_rate,
                'total_pnl': total_pnl,
                'avg_pnl_per_trade': avg_pnl_per_trade,
                'profit_factor': profit_factor,
                'winning_trades': winning_trades,
                'losing_trades': total_trades - winning_trades
            }
            
        except Exception as e:
            logger.error(f"Error getting trade statistics: {e}")
            raise
