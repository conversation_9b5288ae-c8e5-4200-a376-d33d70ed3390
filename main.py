#!/usr/bin/env python3
"""
Real-Time AI-Powered Forex Signal Generation System (XAUUSD)
Main application entry point
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import Optional

# Add src to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from loguru import logger
from dotenv import load_dotenv
import yaml

from src.utils.config import Config
from src.data.collectors.data_collector import DataCollector
from src.models.training.trainer import ModelTrainer
from src.signals.generators.signal_generator import SignalGenerator
from src.signals.delivery.telegram_bot import TelegramBot
from src.api.main import create_app
from src.utils.database import DatabaseManager


class ForexAISystem:
    """Main Forex AI Signal Generation System"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the system"""
        # Load environment variables
        load_dotenv()
        
        # Load configuration
        self.config = Config(config_path)
        
        # Setup logging
        self._setup_logging()
        
        # Initialize components
        self.db_manager = DatabaseManager(self.config)
        self.data_collector = DataCollector(self.config)
        self.model_trainer = ModelTrainer(self.config)
        self.signal_generator = SignalGenerator(self.config)
        self.telegram_bot = TelegramBot(self.config) if self.config.telegram.enabled else None
        
        logger.info("Forex AI System initialized successfully")
    
    def _setup_logging(self):
        """Setup logging configuration"""
        # Remove default logger
        logger.remove()
        
        # Add console logger
        logger.add(
            sys.stdout,
            format=self.config.logging.format,
            level=self.config.logging.level,
            colorize=True
        )
        
        # Add file loggers
        os.makedirs("logs", exist_ok=True)
        
        # Main log file
        logger.add(
            self.config.logging.files.main,
            format=self.config.logging.format,
            level=self.config.logging.level,
            rotation="1 day",
            retention="30 days"
        )
        
        # Error log file
        logger.add(
            self.config.logging.files.errors,
            format=self.config.logging.format,
            level="ERROR",
            rotation="1 day",
            retention="30 days"
        )
    
    async def initialize_data(self):
        """Initialize historical data collection"""
        logger.info("Starting historical data collection...")
        
        try:
            # Create database tables
            await self.db_manager.create_tables()
            
            # Collect historical data for all timeframes
            for timeframe in self.config.trading.timeframes:
                logger.info(f"Collecting {timeframe} data for {self.config.trading.symbol}")
                await self.data_collector.collect_historical_data(
                    symbol=self.config.trading.symbol,
                    timeframe=timeframe,
                    years=self.config.trading.historical_data.years
                )
            
            logger.info("Historical data collection completed")
            
        except Exception as e:
            logger.error(f"Error during data initialization: {e}")
            raise
    
    async def train_models(self):
        """Train AI models"""
        logger.info("Starting model training...")
        
        try:
            # Train all configured models
            for model_name in self.config.ai_models.ensemble_models:
                logger.info(f"Training {model_name} model...")
                await self.model_trainer.train_model(model_name)
            
            logger.info("Model training completed")
            
        except Exception as e:
            logger.error(f"Error during model training: {e}")
            raise
    
    async def start_signal_generation(self):
        """Start real-time signal generation"""
        logger.info("Starting real-time signal generation...")
        
        try:
            # Start signal generation loop
            await self.signal_generator.start_real_time_monitoring()
            
        except Exception as e:
            logger.error(f"Error during signal generation: {e}")
            raise
    
    async def start_telegram_bot(self):
        """Start Telegram bot"""
        if self.telegram_bot:
            logger.info("Starting Telegram bot...")
            await self.telegram_bot.start()
        else:
            logger.info("Telegram bot disabled in configuration")
    
    async def start_web_dashboard(self):
        """Start web dashboard"""
        if self.config.dashboard.enabled:
            logger.info("Starting web dashboard...")
            app = create_app(self.config)
            
            import uvicorn
            config = uvicorn.Config(
                app,
                host=self.config.dashboard.host,
                port=self.config.dashboard.port,
                log_level=self.config.logging.level.lower()
            )
            server = uvicorn.Server(config)
            await server.serve()
        else:
            logger.info("Web dashboard disabled in configuration")
    
    async def run(self, mode: str = "full"):
        """Run the system in different modes"""
        logger.info(f"Starting Forex AI System in {mode} mode")
        
        try:
            if mode == "init":
                # Initialize data only
                await self.initialize_data()
                
            elif mode == "train":
                # Train models only
                await self.train_models()
                
            elif mode == "signals":
                # Generate signals only
                await self.start_signal_generation()
                
            elif mode == "dashboard":
                # Start dashboard only
                await self.start_web_dashboard()
                
            elif mode == "full":
                # Run complete system
                # Create tasks for concurrent execution
                tasks = []
                
                # Signal generation task
                tasks.append(asyncio.create_task(self.start_signal_generation()))
                
                # Telegram bot task
                if self.telegram_bot:
                    tasks.append(asyncio.create_task(self.start_telegram_bot()))
                
                # Web dashboard task
                if self.config.dashboard.enabled:
                    tasks.append(asyncio.create_task(self.start_web_dashboard()))
                
                # Wait for all tasks
                await asyncio.gather(*tasks)
            
            else:
                raise ValueError(f"Unknown mode: {mode}")
                
        except KeyboardInterrupt:
            logger.info("System shutdown requested")
        except Exception as e:
            logger.error(f"System error: {e}")
            raise
        finally:
            logger.info("Forex AI System stopped")


async def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Forex AI Signal Generation System")
    parser.add_argument(
        "--mode",
        choices=["init", "train", "signals", "dashboard", "full"],
        default="full",
        help="System operation mode"
    )
    parser.add_argument(
        "--config",
        default="config.yaml",
        help="Configuration file path"
    )
    
    args = parser.parse_args()
    
    # Create and run system
    system = ForexAISystem(args.config)
    
    if args.mode == "init":
        await system.initialize_data()
    elif args.mode == "train":
        await system.train_models()
    else:
        await system.run(args.mode)


if __name__ == "__main__":
    asyncio.run(main())
