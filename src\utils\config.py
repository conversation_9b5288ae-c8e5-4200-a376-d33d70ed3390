"""
Configuration management for Forex AI System
"""

import os
import yaml
from typing import Any, Dict, Optional
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class DataSourceConfig:
    """Data source configuration"""
    primary: str = "yfinance"
    secondary: str = "alpha_vantage"
    backup: str = "twelvedata"
    alpha_vantage_key: Optional[str] = None
    twelvedata_key: Optional[str] = None


@dataclass
class TradingConfig:
    """Trading configuration"""
    symbol: str = "XAUUSD"
    timeframes: list = field(default_factory=lambda: ["15m", "1h", "4h"])
    
    @dataclass
    class HistoricalData:
        years: int = 5
        start_date: str = "2019-01-01"
    
    @dataclass
    class Signals:
        min_confidence: float = 0.75
        max_signals_per_day: int = 10
        risk_reward_ratio: float = 3.0
        stop_loss_pips: int = 30
        take_profit_pips: int = 90
    
    historical_data: HistoricalData = field(default_factory=HistoricalData)
    signals: Signals = field(default_factory=Signals)


@dataclass
class AIModelsConfig:
    """AI models configuration"""
    primary_model: str = "transformer"
    ensemble_models: list = field(default_factory=lambda: ["transformer", "cnn_lstm", "xgboost"])
    
    @dataclass
    class TransformerConfig:
        sequence_length: int = 100
        d_model: int = 256
        nhead: int = 8
        num_layers: int = 6
        dropout: float = 0.1
    
    @dataclass
    class CNNLSTMConfig:
        cnn_filters: list = field(default_factory=lambda: [32, 64, 128])
        lstm_units: int = 128
        dropout: float = 0.2
    
    @dataclass
    class TrainingConfig:
        batch_size: int = 32
        epochs: int = 100
        learning_rate: float = 0.001
        validation_split: float = 0.2
        early_stopping_patience: int = 10
    
    transformer: TransformerConfig = field(default_factory=TransformerConfig)
    cnn_lstm: CNNLSTMConfig = field(default_factory=CNNLSTMConfig)
    training: TrainingConfig = field(default_factory=TrainingConfig)


@dataclass
class FeaturesConfig:
    """Features configuration"""
    technical_indicators: list = field(default_factory=lambda: [
        "ema_20", "ema_50", "ema_200", "sma_20", "sma_50",
        "rsi_14", "macd", "stoch", "williams_r",
        "atr_14", "bollinger_bands", "keltner_channels",
        "volume_sma", "volume_ratio",
        "higher_highs", "lower_lows", "support_resistance"
    ])
    
    candlestick_patterns: list = field(default_factory=lambda: [
        "doji", "hammer", "engulfing", "shooting_star", "morning_star", "evening_star"
    ])


@dataclass
class DatabaseConfig:
    """Database configuration"""
    type: str = "sqlite"
    path: str = "data/forex_ai.db"
    tables: list = field(default_factory=lambda: [
        "historical_data", "signals", "trades", "model_performance", "feature_importance"
    ])


@dataclass
class TelegramConfig:
    """Telegram configuration"""
    enabled: bool = True
    bot_token: Optional[str] = None
    chat_id: Optional[str] = None
    
    @dataclass
    class Messages:
        signal_template: str = """🚨 *XAUUSD Signal Alert* 🚨

📊 *Direction:* {direction}
📈 *Confidence:* {confidence:.2%}
💰 *Entry:* {entry_price}
🛑 *Stop Loss:* {stop_loss}
🎯 *Take Profit:* {take_profit}

📋 *Analysis:*
• H4 Trend: {h4_trend}
• H1 Momentum: {h1_momentum}
• 15M Pattern: {m15_pattern}

⏰ *Time:* {timestamp}"""
    
    messages: Messages = field(default_factory=Messages)


@dataclass
class DashboardConfig:
    """Dashboard configuration"""
    enabled: bool = True
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    features: list = field(default_factory=lambda: [
        "live_charts", "signal_history", "model_performance", "trade_analytics"
    ])


@dataclass
class BacktestingConfig:
    """Backtesting configuration"""
    initial_capital: float = 10000
    commission: float = 0.0001
    slippage: float = 0.0001
    metrics: list = field(default_factory=lambda: [
        "total_return", "sharpe_ratio", "max_drawdown", "win_rate", "profit_factor"
    ])


@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    
    @dataclass
    class Files:
        main: str = "logs/forex_ai.log"
        signals: str = "logs/signals.log"
        trades: str = "logs/trades.log"
        errors: str = "logs/errors.log"
    
    files: Files = field(default_factory=Files)


@dataclass
class AWSConfig:
    """AWS configuration"""
    region: str = "us-east-1"
    instance_type: str = "t3.medium"
    
    @dataclass
    class DockerConfig:
        image_name: str = "forex-ai-system"
        container_port: int = 8000
    
    @dataclass
    class ScalingConfig:
        min_instances: int = 1
        max_instances: int = 3
        target_cpu_utilization: int = 70
    
    docker: DockerConfig = field(default_factory=DockerConfig)
    scaling: ScalingConfig = field(default_factory=ScalingConfig)


@dataclass
class MonitoringConfig:
    """Monitoring configuration"""
    enabled: bool = True
    
    @dataclass
    class HealthChecks:
        data_feed: int = 300
        model_prediction: int = 60
        signal_generation: int = 120
    
    @dataclass
    class Alerts:
        max_consecutive_losses: int = 5
        max_drawdown_percent: int = 10
        min_daily_signals: int = 1
    
    health_checks: HealthChecks = field(default_factory=HealthChecks)
    alerts: Alerts = field(default_factory=Alerts)


class Config:
    """Main configuration class"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize configuration from YAML file"""
        self.config_path = config_path
        self._load_config()
        self._setup_environment_variables()
    
    def _load_config(self):
        """Load configuration from YAML file"""
        if not Path(self.config_path).exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        
        with open(self.config_path, 'r') as file:
            config_data = yaml.safe_load(file)
        
        # Initialize configuration sections
        self.data_sources = DataSourceConfig(**config_data.get('data_sources', {}))
        self.trading = TradingConfig(**config_data.get('trading', {}))
        self.ai_models = AIModelsConfig(**config_data.get('ai_models', {}))
        self.features = FeaturesConfig(**config_data.get('features', {}))
        self.database = DatabaseConfig(**config_data.get('database', {}))
        self.telegram = TelegramConfig(**config_data.get('telegram', {}))
        self.dashboard = DashboardConfig(**config_data.get('dashboard', {}))
        self.backtesting = BacktestingConfig(**config_data.get('backtesting', {}))
        self.logging = LoggingConfig(**config_data.get('logging', {}))
        self.aws = AWSConfig(**config_data.get('aws', {}))
        self.monitoring = MonitoringConfig(**config_data.get('monitoring', {}))
    
    def _setup_environment_variables(self):
        """Setup environment variables"""
        # API Keys
        self.data_sources.alpha_vantage_key = os.getenv('ALPHA_VANTAGE_API_KEY')
        self.data_sources.twelvedata_key = os.getenv('TWELVEDATA_API_KEY')
        
        # Telegram
        self.telegram.bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        self.telegram.chat_id = os.getenv('TELEGRAM_CHAT_ID')
        
        # Database
        database_url = os.getenv('DATABASE_URL')
        if database_url:
            self.database.path = database_url
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key"""
        keys = key.split('.')
        value = self
        
        for k in keys:
            if hasattr(value, k):
                value = getattr(value, k)
            else:
                return default
        
        return value
    
    def update(self, key: str, value: Any):
        """Update configuration value"""
        keys = key.split('.')
        obj = self
        
        for k in keys[:-1]:
            if hasattr(obj, k):
                obj = getattr(obj, k)
            else:
                raise KeyError(f"Configuration key not found: {key}")
        
        setattr(obj, keys[-1], value)
